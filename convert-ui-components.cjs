const fs = require('fs');
const path = require('path');

const uiDir = './src/components/ui';

// Get all .jsx files in the ui directory
const files = fs.readdirSync(uiDir).filter(file => file.endsWith('.jsx'));

files.forEach(file => {
  const filePath = path.join(uiDir, file);
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Remove TypeScript type imports
  content = content.replace(/import\s*\{([^}]*),\s*type\s+[^}]*\}\s*from/g, 'import {$1} from');
  content = content.replace(/,\s*type\s+[^,}]*/g, '');
  
  // Remove interface declarations
  content = content.replace(/export\s+interface\s+\w+[^{]*\{[^}]*\}/gs, '');
  
  // Remove type annotations from React.forwardRef
  content = content.replace(/React\.forwardRef<[^>]*,\s*[^>]*>/g, 'React.forwardRef');
  
  // Remove type annotations from function parameters
  content = content.replace(/\(\s*\{([^}]*)\}\s*:\s*[^)]*\)/g, '({$1})');
  
  // Remove 'as' type assertions
  content = content.replace(/\s+as\s+\w+/g, '');
  
  // Clean up extra whitespace
  content = content.replace(/\n\n\n+/g, '\n\n');
  
  fs.writeFileSync(filePath, content);
  console.log(`Converted ${file}`);
});

console.log('UI components conversion completed!');
